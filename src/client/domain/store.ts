import {configureStore} from "@reduxjs/toolkit";
import {deckBuilderReducer} from "@/src/client/domain/DeckBuilder/deckBuilderReducer";
import {catalogReducer} from "@/src/client/domain/Catalog/catalogReducer";
import {catalogFiltersReducer} from "@/src/client/domain/Catalog/catalogFiltersReducer";
import {LocationService} from "@/src/client/application/services/LocationService";
import {DeckDraftService} from "@/src/client/application/services/DeckDraftService";
import {catalogSearchReducer} from "@/src/client/domain/Catalog/catalogSearchReducer";
import {BrowserLocationService} from "@/src/client/infrastructure/services/location/browserLocationService";
import {BrowserDeckDraftService} from "@/src/client/infrastructure/services/deckDraft/browserDeckDraftService";
import {gameSettingsReducer} from "@/src/client/domain/GameSettings/gameSettingsReducer";

export const locationService = new BrowserLocationService();
export let deckDraftService: DeckDraftService = new BrowserDeckDraftService();
export const setDeckDraftService = (service: DeckDraftService) => {
  deckDraftService = service;
};

export const store = configureStore({
  reducer: {
    deckBuilder: deckBuilderReducer,
    catalog: catalogReducer,
    catalogFilters: catalogFiltersReducer,
    catalogSearch: catalogSearchReducer,
    gameSettings: gameSettingsReducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService, deckDraftService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type ThunkExtra = { locationService: LocationService; deckDraftService: DeckDraftService };
