import {useDispatch, useSelector} from "react-redux";
import {useCallback} from "react";
import {addCardToDeck} from "@/src/client/application/commands/addCardToDeck/addCardToDeck";
import {AppDispatch} from "@/src/client/infrastructure/store";
import {getTotalCardsInDeck} from "@/src/client/application/queries/getTotalCardsInDeck";
import {getCardsInDeck} from "@/src/client/application/queries/getCardsInDeck";
import {removeCardFromDeck} from "@/src/client/application/commands/removeCardFromDeck/removeCardFromDeck";
import {showCardDetails} from "@/src/client/application/commands/showCardDetails/showCardDetails";
import {getCardDetails} from "@/src/client/application/queries/getCardDetails";
import {hideCardDetails} from "@/src/client/application/commands/hideCardDetails/hideCardDetails";
import {DeckBuilderCard} from "@/src/client/domain/DeckBuilder/DeckBuilderCard";
import {filterCatalog} from "@/src/client/application/commands/filterCatalog/filterCatalog";
import {search as searchCommand} from "@/src/client/application/commands/search/search";
import {getActiveFilters} from "@/src/client/application/queries/getActiveFilters";
import {getSearchTerm} from "@/src/client/application/queries/getSearchTerm";
import {Filter} from "@/src/client/domain/Catalog/Filter";
import {updateAvailableFilters} from "@/src/client/application/commands/updateAvailableFilters/updateAvailableFilters";
import {getDeckBuilderView} from "@/src/client/application/queries/getDeckBuilderView";
import {getDeckName} from "@/src/client/application/queries/getDeckName";
import {switchDeckBuilderView} from "@/src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView";

export const useDeckBuilder = (): {
  deckCards: DeckBuilderCard[];
  totalCardsInDeck: number;
  addCardToDeck: (cardId: string) => void;
  removeCard: (cardId: string) => void;
  showCardDetails: (cardId: string) => void;
  hideCardDetails: () => void;
  cardDetails: { cardImage: string; cardName: string } | null;
  filterCatalog: (filters: string[]) => void;
  searchCatalog: (search: string) => void;
  activeFilters: string[];
  search: string;
  loadAvailableFilters: (filters: Filter[]) => void;
  view: 'catalog' | 'deck';
  name: string | null;
  switchView: (view: 'catalog' | 'deck') => void;
} => {
  const dispatch = useDispatch<AppDispatch>();

  return {
    activeFilters: useSelector(getActiveFilters),
    search: useSelector(getSearchTerm),
    deckCards: useSelector(getCardsInDeck),
    totalCardsInDeck: useSelector(getTotalCardsInDeck),
    cardDetails: useSelector(getCardDetails),
    view: useSelector(getDeckBuilderView),
    name: useSelector(getDeckName),
    showCardDetails: useCallback((cardId: string) => dispatch(showCardDetails({cardId})), [dispatch]),
    hideCardDetails: useCallback(() => dispatch(hideCardDetails()), [dispatch]),
    addCardToDeck: useCallback((cardId: string) => dispatch(addCardToDeck({cardId})), [dispatch]),
    removeCard: useCallback((cardId: string) => dispatch(removeCardFromDeck({cardId})), [dispatch]),
    filterCatalog: useCallback((filters: string[]) => dispatch(filterCatalog({filters})), [dispatch]),
    searchCatalog: useCallback((search: string) => dispatch(searchCommand({search})), [dispatch]),
    loadAvailableFilters: useCallback((filters: Filter[]) => dispatch(updateAvailableFilters({filters})), [dispatch]),
    switchView: useCallback((view: 'catalog' | 'deck') => dispatch(switchDeckBuilderView({view})), [dispatch]),
  };
};
