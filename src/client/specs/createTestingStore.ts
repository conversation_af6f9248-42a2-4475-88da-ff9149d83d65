import {configureStore} from "@reduxjs/toolkit";
import {RootState} from "../infrastructure/store";
import {catalogReducer, initialCatalogState} from "@/src/client/domain/Catalog/catalogReducer";
import {deckBuilderReducer, initialDeckBuilderState} from "@/src/client/domain/DeckBuilder/deckBuilderReducer";
import {deepMerge} from "@/src/client/specs/utils/deepMerge";
import {catalogSearchReducer, initialCatalogSearchState} from "@/src/client/domain/Catalog/catalogSearchReducer";
import {catalogFiltersReducer, initialCatalogFiltersState} from "@/src/client/domain/Catalog/catalogFiltersReducer";
import {gameSettingsReducer, initialGameSettingsState} from "@/src/client/domain/GameSettings/gameSettingsReducer";

export const rootReducers = {
  deckBuilder: deckBuilderReducer,
  catalog: catalogReducer,
  catalogFilters: catalogFiltersReducer,
  catalogSearch: catalogSearchReducer,
  gameSettings: gameSettingsReducer,
};

export const rootInitialState = {
  deckBuilder: initialDeckBuilderState,
  catalog: initialCatalogState,
  catalogFilters: initialCatalogFiltersState,
  catalogSearch: initialCatalogSearchState,
  gameSettings: initialGameSettingsState,
};

type OverrideState = Partial<{
  [K in keyof RootState]: Partial<RootState[K]>
}>;

import {LocationService} from "@/src/client/application/services/LocationService";
import {DeckDraftService} from "@/src/client/application/services/DeckDraftService";
import {FakeLocationService} from "@/src/client/specs/fakes/FakeLocationService";
import {FakeDeckDraftService} from "@/src/client/specs/fakes/FakeDeckDraftService";
import {subscribeToDeckDraft} from "@/src/client/application/subscribers/subscribeToDeckDraft";
import {setDeckDraftService} from "@/src/client/infrastructure/store";

export const createTestingStore = (
  overrides: OverrideState = {},
  extra?: { locationService?: LocationService; deckDraftService?: DeckDraftService; withDraftSubscriber?: boolean }
) => {
  const deckDraft = extra?.deckDraftService ?? new FakeDeckDraftService();
  setDeckDraftService(deckDraft);
  const store = configureStore({
    reducer: rootReducers,
    preloadedState: Object.fromEntries(
      Object.entries(rootInitialState).map(([key, initial]) => {
        const override = overrides[key as keyof RootState];
        const merged = override ? deepMerge(initial, override) : initial;
        return [key, merged];
      })
    ) as RootState,
    middleware: (getDefaultMiddleware) => {
      const base = getDefaultMiddleware({
        thunk: {
          extraArgument: {
            locationService: extra?.locationService ?? new FakeLocationService(),
            deckDraftService: deckDraft,
          },
        },
      });
      return base;
    },
  });
  if (extra?.withDraftSubscriber) {
    subscribeToDeckDraft(store);
  }
  return store;
}
