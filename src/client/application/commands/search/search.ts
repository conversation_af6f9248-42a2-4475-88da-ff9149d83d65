import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchUpdatedEvent} from '@/src/client/domain/Catalog/catalogSearchEvents';
import {RootState, ThunkExtra} from '@/src/client/infrastructure/store';

export const search = createAsyncThunk<void, { search: string }, { state: RootState; extra: ThunkExtra }>(
  'catalog/search',
  async ({search}, {dispatch, extra}) => {
    dispatch(searchUpdatedEvent({search}));
    extra.locationService.setSearch(search);
  }
);
