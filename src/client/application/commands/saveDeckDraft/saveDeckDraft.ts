import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState, ThunkExtra} from '@/src/client/domain/store';

const buildDraft = (state: RootState['deckBuilder']) => ({
  name: state.name,
  cards: Object.values(state.cardsInDeck).map(({card, quantity}) => ({
    cardId: card.id,
    quantity,
  })),
});

export const saveDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/saveDraft',
  async (_, {getState, extra: {deckDraftService}}) => {
    const draft = buildDraft(getState().deckBuilder);
    deckDraftService.saveDraft(draft);
  }
);

export type BuildDraftFromState = typeof buildDraft;
