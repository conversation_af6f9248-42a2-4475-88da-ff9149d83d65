import {createAsyncThunk} from "@reduxjs/toolkit";
import {filterCatalog} from "@/src/client/application/commands/filterCatalog/filterCatalog";
import {search as searchCommand} from "@/src/client/application/commands/search/search";
import {RootState, ThunkExtra} from "@/src/client/infrastructure/store";

export const initializeDeckBuilderFromLocation = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/initFromLocation',
  async (_, {dispatch, extra: {locationService}}) => {
    const filters = locationService.getFilters();
    const search = locationService.getSearch();
    if (filters.length > 0) {
      await dispatch(filterCatalog({filters}));
    }
    if (search) {
      await dispatch(searchCommand({search}));
    }
  }
);
