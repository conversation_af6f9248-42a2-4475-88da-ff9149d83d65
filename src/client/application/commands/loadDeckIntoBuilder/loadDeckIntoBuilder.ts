import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState} from "@/src/client/infrastructure/store";
import {deckLoadedEvent} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {getCatalogCardById} from "@/src/client/application/queries/getCatalogCardById";

export const loadDeckIntoBuilder = createAsyncThunk<void, {deck: {name: string; cards: {cardId: string; quantity: number}[]}}, {state: RootState}>(
  'deckBuilder/loadDeck',
  async ({deck}, {dispatch, getState}) => {
    const state = getState();
    const deckCards = deck.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckLoadedEvent({name: deck.name, cards: deckCards}));
  }
);
