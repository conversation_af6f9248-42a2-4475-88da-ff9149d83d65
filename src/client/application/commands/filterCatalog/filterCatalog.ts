import {createAsyncThunk} from "@reduxjs/toolkit";
import {CatalogFilters} from "@/src/client/domain/Catalog/CatalogFilters";
import {RootState, ThunkExtra} from "@/src/client/infrastructure/store";

const FILTER_CATALOG_EVENT_TYPE = 'catalog/filterCatalog';

export const filterCatalog = createAsyncThunk<void, { filters: string[] }, {
  state: RootState;
  extra: ThunkExtra;
}>(FILTER_CATALOG_EVENT_TYPE, async ({filters}, {dispatch, getState, extra}) => {
  const catalogFilters = CatalogFilters.fromState(getState().catalogFilters);
  const events = catalogFilters.applyFilters(filters);
  events.forEach(dispatch);
  extra.locationService.setFilters(catalogFilters.toState().active.map(f => f.name));
});
