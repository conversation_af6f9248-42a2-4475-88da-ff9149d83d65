import {AnyAction, Store} from '@reduxjs/toolkit';
import {RootState, AppDispatch} from '@/src/client/domain/store';
import {saveDeckDraft} from '@/src/client/application/commands/saveDeckDraft/saveDeckDraft';

export const subscribeToDeckDraft = (
  store: Store<RootState, AnyAction> & {dispatch: AppDispatch},
) => {
  if (typeof window === 'undefined') {
    return;
  }

  let previous = JSON.stringify({});

  const unsubscribe = store.subscribe(() => {
    try {
      const state = store.getState();
      const draft = {
        name: state.deckBuilder.name,
        cards: Object.values(state.deckBuilder.cardsInDeck).map(({card, quantity}) => ({
          cardId: card.id,
          quantity,
        })),
      };
      const current = JSON.stringify(draft);
      if (current !== previous) {
        previous = current;
        store.dispatch(saveDeckDraft());
      }
    } catch (error) {
      console.error('Error in deck draft subscriber:', error);
    }
  });

  return unsubscribe;
};
