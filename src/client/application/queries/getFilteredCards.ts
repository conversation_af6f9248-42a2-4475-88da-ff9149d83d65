import {createSelector} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/infrastructure/store';
import {applyFilterToCard} from './applyFilterToCard';

export const getFilteredCards = createSelector(
  [
    (state: RootState) => state.catalog.cards,
    (state: RootState) => state.catalogFilters.active,
    (state: RootState) => state.catalogFilters.available,
    (state: RootState) => state.deckBuilder.view,
    (state: RootState) => state.deckBuilder.cardsInDeck,
    (state: RootState) => state.catalogSearch.search,
  ],
  (cards, activeFilters, availableFilters, view, cardsInDeck, search) => {
    const filtersByProp = groupBy(activeFilters, f => f.dataProperty);
    const allFilterOptionsByProp = groupBy(availableFilters, f => f.dataProperty);

    return Object.values(cards)
      .filter(card => {
        const passesFilters = Object.entries(filtersByProp).every(([prop, filters]) => {
          const totalOptions = allFilterOptionsByProp[prop]?.map(f => f.name).sort().join(',') ?? '';
          const current = filters.map(f => f.name).sort().join(',');

          if (totalOptions === current) return true;

          return filters.some(filter => applyFilterToCard(card, filter));
        });

        if (!passesFilters) return false;

        const matchesSearch = search
          ? card.name.toLowerCase().includes(search.toLowerCase())
          : true;
        if (!matchesSearch) return false;

        if (view === 'deck') {
          return card.id in cardsInDeck;
        }

        return true;
      });
  }
);

function groupBy<T>(items: T[], key: (item: T) => string): Record<string, T[]> {
  return items.reduce((acc, item) => {
    const group = key(item);
    if (!acc[group]) acc[group] = [];
    acc[group].push(item);
    return acc;
  }, {} as Record<string, T[]>);
}
